# Load required libraries
if (!require(ggplot2)) install.packages("ggplot2")
if (!require(corrplot)) install.packages("corrplot")
if (!require(gridExtra)) install.packages("gridExtra")
if (!require(reshape2)) install.packages("reshape2")

library(ggplot2)
library(corrplot)
library(gridExtra)
library(reshape2)

# Load the data
data <- read.csv("research_data.csv")

# Print data structure
print("Data Structure:")
print(data)

# Calculate correlation matrix
numeric_data <- data[, c("Recast", "Expansion", "Open_Question", "Child_Word_Count", "Mean_Length_Utterance")]
cor_matrix <- cor(numeric_data)

# Print correlation matrix
print("Correlation Matrix:")
print(round(cor_matrix, 3))

# Create correlation plot
png("correlation_matrix.png", width = 800, height = 600)
corrplot(cor_matrix, method = "color", type = "upper", 
         addCoef.col = "black", tl.col = "black", tl.srt = 45,
         title = "Correlation Matrix: Independent vs Dependent Variables")
dev.off()

# Create scatter plots
p1 <- ggplot(data, aes(x = Recast, y = Child_Word_Count)) +
  geom_point(size = 3, color = "blue", alpha = 0.7) +
  geom_smooth(method = "lm", se = TRUE, color = "red") +
  geom_text(aes(label = Session), vjust = -0.5, hjust = 0.5) +
  labs(title = "Recast vs Child Word Count", x = "Recast Frequency", y = "Child Word Count") +
  theme_minimal()

p2 <- ggplot(data, aes(x = Expansion, y = Child_Word_Count)) +
  geom_point(size = 3, color = "orange", alpha = 0.7) +
  geom_smooth(method = "lm", se = TRUE, color = "red") +
  geom_text(aes(label = Session), vjust = -0.5, hjust = 0.5) +
  labs(title = "Expansion vs Child Word Count", x = "Expansion Frequency", y = "Child Word Count") +
  theme_minimal()

p3 <- ggplot(data, aes(x = Open_Question, y = Child_Word_Count)) +
  geom_point(size = 3, color = "purple", alpha = 0.7) +
  geom_smooth(method = "lm", se = TRUE, color = "red") +
  geom_text(aes(label = Session), vjust = -0.5, hjust = 0.5) +
  labs(title = "Open Question vs Child Word Count", x = "Open Question Frequency", y = "Child Word Count") +
  theme_minimal()

p4 <- ggplot(data, aes(x = Recast, y = Mean_Length_Utterance)) +
  geom_point(size = 3, color = "blue", alpha = 0.7) +
  geom_smooth(method = "lm", se = TRUE, color = "red") +
  geom_text(aes(label = Session), vjust = -0.5, hjust = 0.5) +
  labs(title = "Recast vs Mean Length of Utterance", x = "Recast Frequency", y = "Mean Length of Utterance") +
  theme_minimal()

# Save scatter plots
png("scatter_plots.png", width = 1200, height = 800)
grid.arrange(p1, p2, p3, p4, ncol = 2)
dev.off()

# Create trend lines
data$Session_Numeric <- 1:nrow(data)

p5 <- ggplot(data, aes(x = Session_Numeric)) +
  geom_line(aes(y = Child_Word_Count, color = "Child Word Count"), size = 1.2) +
  geom_point(aes(y = Child_Word_Count, color = "Child Word Count"), size = 3) +
  geom_line(aes(y = Mean_Length_Utterance * 10, color = "MLU (x10)"), size = 1.2) +
  geom_point(aes(y = Mean_Length_Utterance * 10, color = "MLU (x10)"), size = 3) +
  scale_x_continuous(breaks = 1:5, labels = data$Session) +
  labs(title = "Dependent Variables Progression Across Sessions",
       x = "Training Sessions", y = "Values", color = "Variables") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

png("trend_analysis.png", width = 800, height = 600)
print(p5)
dev.off()

# Calculate and print detailed correlations
cat("\n=== DETAILED CORRELATION ANALYSIS ===\n")
independent_vars <- c("Recast", "Expansion", "Open_Question")
dependent_vars <- c("Child_Word_Count", "Mean_Length_Utterance")

for (indep in independent_vars) {
  cat(paste("\n", toupper(indep), ":\n"))
  for (dep in dependent_vars) {
    cor_test <- cor.test(data[[indep]], data[[dep]])
    cat(paste("  vs", dep, ": r =", round(cor_test$estimate, 3), 
              ", p =", round(cor_test$p.value, 3), "\n"))
  }
}

print("Analysis complete! Check the generated PNG files for visualizations.")
