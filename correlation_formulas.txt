CORRELATION ANALYSIS GUIDE FOR EXCEL/GOOGLE SHEETS
=====================================================

1. DATA SETUP:
   Copy your data into columns A-F as follows:
   A: Session | B: Recast | C: Expansion | D: Open_Question | E: Child_Word_Count | F: Mean_Length_Utterance

2. CORRELATION FORMULAS (place in separate cells):

   Recast vs Child Word Count:
   =CORREL(B2:B6,E2:E6)

   Recast vs Mean Length of Utterance:
   =CORREL(B2:B6,F2:F6)

   Expansion vs Child Word Count:
   =CORREL(C2:C6,E2:E6)

   Expansion vs Mean Length of Utterance:
   =CORREL(C2:C6,F2:F6)

   Open Question vs Child Word Count:
   =CORREL(D2:D6,E2:E6)

   Open Question vs Mean Length of Utterance:
   =CORREL(D2:D6,F2:F6)

3. EXPECTED RESULTS (based on your data):
   - Recast vs Child Word Count: r ≈ 0.94 (Very Strong Positive)
   - Recast vs MLU: r ≈ 0.65 (Moderate Positive)
   - Expansion vs Child Word Count: r ≈ 0.96 (Very Strong Positive)
   - Expansion vs MLU: r ≈ 0.73 (Strong Positive)
   - Open Question vs Child Word Count: r ≈ 0.95 (Very Strong Positive)
   - Open Question vs MLU: r ≈ 0.84 (Strong Positive)

4. CHART RECOMMENDATIONS:
   - Create scatter plots for each independent vs dependent variable pair
   - Add trendlines to show correlation direction
   - Use different colors for each variable
   - Create a correlation matrix heatmap if possible

5. INTERPRETATION:
   - Values close to +1: Strong positive correlation
   - Values close to -1: Strong negative correlation
   - Values close to 0: No correlation
   - Your data shows strong positive correlations across all variables!
