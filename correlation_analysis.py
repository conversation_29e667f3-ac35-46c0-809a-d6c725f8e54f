import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

# Load the data
df = pd.read_csv('research_data.csv')

# Set up the plotting style
plt.style.use('default')
sns.set_palette("husl")

# Create figure with multiple subplots
fig = plt.figure(figsize=(20, 15))

# 1. Correlation Matrix Heatmap
ax1 = plt.subplot(3, 3, 1)
# Select only numeric columns for correlation
numeric_cols = ['Recast', 'Expansion', 'Open_Question', 'Child_Word_Count', 'Mean_Length_Utterance']
corr_matrix = df[numeric_cols].corr()
sns.heatmap(corr_matrix, annot=True, cmap='RdYlBu_r', center=0, 
            square=True, fmt='.3f', cbar_kws={'shrink': 0.8})
plt.title('Correlation Matrix: All Variables', fontsize=12, fontweight='bold')

# 2. Independent Variables vs Child Word Count
ax2 = plt.subplot(3, 3, 2)
x_pos = np.arange(len(df))
width = 0.25
plt.bar(x_pos - width, df['Recast'], width, label='Recast', alpha=0.8)
plt.bar(x_pos, df['Expansion'], width, label='Expansion', alpha=0.8)
plt.bar(x_pos + width, df['Open_Question'], width, label='Open Question', alpha=0.8)
plt.xlabel('Training Sessions')
plt.ylabel('Frequency')
plt.title('Independent Variables Across Sessions', fontweight='bold')
plt.xticks(x_pos, df['Session'], rotation=45)
plt.legend()
plt.grid(True, alpha=0.3)

# 3. Child Word Count Trend
ax3 = plt.subplot(3, 3, 3)
plt.plot(df['Session'], df['Child_Word_Count'], marker='o', linewidth=3, markersize=8, color='red')
plt.xlabel('Training Sessions')
plt.ylabel('Child Word Count')
plt.title('Child Word Count Progression', fontweight='bold')
plt.xticks(rotation=45)
plt.grid(True, alpha=0.3)

# 4. Mean Length of Utterance Trend
ax4 = plt.subplot(3, 3, 4)
plt.plot(df['Session'], df['Mean_Length_Utterance'], marker='s', linewidth=3, markersize=8, color='green')
plt.xlabel('Training Sessions')
plt.ylabel('Mean Length of Utterance')
plt.title('Mean Length of Utterance Progression', fontweight='bold')
plt.xticks(rotation=45)
plt.grid(True, alpha=0.3)

# 5. Scatter Plot: Recast vs Child Word Count
ax5 = plt.subplot(3, 3, 5)
plt.scatter(df['Recast'], df['Child_Word_Count'], s=100, alpha=0.7, color='blue')
for i, session in enumerate(df['Session']):
    plt.annotate(session, (df['Recast'].iloc[i], df['Child_Word_Count'].iloc[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=9)
plt.xlabel('Recast Frequency')
plt.ylabel('Child Word Count')
plt.title('Recast vs Child Word Count', fontweight='bold')
plt.grid(True, alpha=0.3)

# 6. Scatter Plot: Expansion vs Child Word Count
ax6 = plt.subplot(3, 3, 6)
plt.scatter(df['Expansion'], df['Child_Word_Count'], s=100, alpha=0.7, color='orange')
for i, session in enumerate(df['Session']):
    plt.annotate(session, (df['Expansion'].iloc[i], df['Child_Word_Count'].iloc[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=9)
plt.xlabel('Expansion Frequency')
plt.ylabel('Child Word Count')
plt.title('Expansion vs Child Word Count', fontweight='bold')
plt.grid(True, alpha=0.3)

# 7. Scatter Plot: Open Question vs Child Word Count
ax7 = plt.subplot(3, 3, 7)
plt.scatter(df['Open_Question'], df['Child_Word_Count'], s=100, alpha=0.7, color='purple')
for i, session in enumerate(df['Session']):
    plt.annotate(session, (df['Open_Question'].iloc[i], df['Child_Word_Count'].iloc[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=9)
plt.xlabel('Open Question Frequency')
plt.ylabel('Child Word Count')
plt.title('Open Question vs Child Word Count', fontweight='bold')
plt.grid(True, alpha=0.3)

# 8. Combined Independent Variables vs Mean Length of Utterance
ax8 = plt.subplot(3, 3, 8)
plt.scatter(df['Recast'], df['Mean_Length_Utterance'], s=100, alpha=0.7, label='Recast', color='blue')
plt.scatter(df['Expansion'], df['Mean_Length_Utterance'], s=100, alpha=0.7, label='Expansion', color='orange')
plt.scatter(df['Open_Question'], df['Mean_Length_Utterance'], s=100, alpha=0.7, label='Open Question', color='purple')
plt.xlabel('Frequency')
plt.ylabel('Mean Length of Utterance')
plt.title('Independent Variables vs MLU', fontweight='bold')
plt.legend()
plt.grid(True, alpha=0.3)

# 9. Summary Statistics Table
ax9 = plt.subplot(3, 3, 9)
ax9.axis('tight')
ax9.axis('off')

# Calculate correlations
correlations = []
variables = ['Recast', 'Expansion', 'Open_Question']
outcomes = ['Child_Word_Count', 'Mean_Length_Utterance']

for var in variables:
    for outcome in outcomes:
        corr, p_value = pearsonr(df[var], df[outcome])
        correlations.append([var, outcome, f'{corr:.3f}', f'{p_value:.3f}'])

corr_df = pd.DataFrame(correlations, columns=['Independent Var', 'Dependent Var', 'Correlation', 'P-value'])
table = ax9.table(cellText=corr_df.values, colLabels=corr_df.columns,
                  cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
table.auto_set_font_size(False)
table.set_fontsize(9)
table.scale(1, 1.5)
ax9.set_title('Correlation Coefficients', fontweight='bold', pad=20)

plt.tight_layout()
plt.savefig('correlation_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# Print detailed correlation analysis
print("="*60)
print("DETAILED CORRELATION ANALYSIS")
print("="*60)
print("\nCorrelation Matrix:")
print(corr_matrix.round(3))

print("\n" + "="*60)
print("INDIVIDUAL CORRELATIONS WITH SIGNIFICANCE")
print("="*60)

for var in variables:
    print(f"\n{var.upper()}:")
    for outcome in outcomes:
        corr, p_value = pearsonr(df[var], df[outcome])
        significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else "ns"
        print(f"  vs {outcome}: r = {corr:.3f}, p = {p_value:.3f} {significance}")

print("\n" + "="*60)
print("INTERPRETATION GUIDE")
print("="*60)
print("Correlation strength:")
print("  0.00-0.30: Weak")
print("  0.30-0.70: Moderate") 
print("  0.70-1.00: Strong")
print("\nSignificance levels:")
print("  *** p < 0.001 (highly significant)")
print("  **  p < 0.01  (very significant)")
print("  *   p < 0.05  (significant)")
print("  ns  p ≥ 0.05  (not significant)")
